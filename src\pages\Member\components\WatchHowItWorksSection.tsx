import React, { useState } from "react";

const WatchHowItWorksSection = () => {
  const [isPlaying, setIsPlaying] = useState(false);

  const handlePlayClick = () => {
    setIsPlaying(true);
    // In a real implementation, you would integrate with a video player
    console.log("Playing video...");
  };

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold" style={{ color: "#0D3166" }}>
            Watch How It Works
          </h2>
        </div>

        {/* Video Container */}
        <div className="max-w-4xl mx-auto">
          <div className="relative rounded-2xl overflow-hidden shadow-2xl">
            {/* Video Background Image */}
            <div 
              className="relative w-full h-0 pb-[56.25%] bg-cover bg-center"
              style={{
                backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQ1MCIgdmlld0JveD0iMCAwIDgwMCA0NTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI4MDAiIGhlaWdodD0iNDUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxyZWN0IHg9IjEwMCIgeT0iMTAwIiB3aWR0aD0iNjAwIiBoZWlnaHQ9IjI1MCIgcng9IjEwIiBmaWxsPSIjRTVFN0VCIi8+Cjx0ZXh0IHg9IjQwMCIgeT0iMjQwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiPkJ1c2luZXNzIE1lZXRpbmcgVmlkZW88L3RleHQ+Cjwvc3ZnPgo=')`
              }}
            >
              {/* Play Button Overlay */}
              {!isPlaying && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <button
                    onClick={handlePlayClick}
                    className="group relative"
                    aria-label="Play video"
                  >
                    {/* Outer Ring */}
                    <div className="w-24 h-24 rounded-full bg-white bg-opacity-20 backdrop-blur-sm flex items-center justify-center group-hover:bg-opacity-30 transition-all duration-300">
                      {/* Inner Ring */}
                      <div className="w-16 h-16 rounded-full bg-white bg-opacity-90 flex items-center justify-center group-hover:bg-opacity-100 transition-all duration-300 shadow-lg">
                        {/* Play Icon */}
                        <svg 
                          className="w-6 h-6 text-gray-700 ml-1" 
                          fill="currentColor" 
                          viewBox="0 0 24 24"
                        >
                          <path d="M8 5v14l11-7z"/>
                        </svg>
                      </div>
                    </div>
                  </button>
                </div>
              )}

              {/* Video Player Placeholder */}
              {isPlaying && (
                <div className="absolute inset-0 bg-black flex items-center justify-center">
                  <div className="text-white text-center">
                    <div className="w-16 h-16 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                    <p className="text-lg">Loading video...</p>
                  </div>
                </div>
              )}
            </div>

            {/* Video Info Overlay */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black via-black/50 to-transparent p-6">
              <div className="text-white">
                <h3 className="text-xl font-semibold mb-2">
                  Learn How eBaDollar Works
                </h3>
                <p className="text-gray-200 text-sm">
                  Watch this comprehensive guide to understand how eBaDollar can transform your financial transactions
                </p>
              </div>
            </div>
          </div>

          {/* Video Description */}
          <div className="text-center mt-8">
            <p className="text-gray-600 text-lg max-w-2xl mx-auto leading-relaxed">
              Discover how eBaDollar revolutionizes digital transactions with our comprehensive video guide. 
              Learn about our secure platform, earning opportunities, and how to get started today.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WatchHowItWorksSection;
