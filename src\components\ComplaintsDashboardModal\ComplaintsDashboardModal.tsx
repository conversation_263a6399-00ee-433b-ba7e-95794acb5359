import React, { useState } from "react";
import { Modal } from "../Modal";
import { Skeleton } from "../Skeleton";
import {
  useComplaintsAsSellerQuery,
  useComplaintsAsBuyerQuery,
} from "../../query/useDeliveryTasks";

interface Complaint {
  id: string;
  customerName: string;
  customerAvatar: string;
  date: string;
  rating: number;
  complaintText: string;
  orderId: string;
  type: "seller" | "buyer";
}

interface ComplaintsDashboardModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ComplaintsDashboardModal: React.FC<ComplaintsDashboardModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [activeTab, setActiveTab] = useState<"seller" | "buyer">("seller");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  // Query hooks
  const { data: sellerComplaintsData, isLoading: sellerLoading } =
    useComplaintsAsSellerQuery({
      page: currentPage,
      limit: itemsPerPage,
    });

  const { data: buyerComplaintsData, isLoading: buyerLoading } =
    useComplaintsAsBuyerQuery({
      page: currentPage,
      limit: itemsPerPage,
    });

  // Get data from queries
  const sellerComplaints = sellerComplaintsData?.data || [];
  const buyerComplaints = buyerComplaintsData?.data || [];
  const sellerPagination = sellerComplaintsData?.pagination;
  const buyerPagination = buyerComplaintsData?.pagination;

  const currentComplaints =
    activeTab === "seller" ? sellerComplaints : buyerComplaints;
  const currentPagination =
    activeTab === "seller" ? sellerPagination : buyerPagination;
  const isLoading = activeTab === "seller" ? sellerLoading : buyerLoading;

  const renderStars = (rating: number) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <svg
            key={star}
            className={`w-4 h-4 ${
              star <= rating ? "text-yellow-400" : "text-gray-300"
            }`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
      </div>
    );
  };

  const handleRespond = (complaintId: string) => {
    console.log("Responding to complaint:", complaintId);
  };

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={onClose}
      title=""
      modalHeader={false}
      classes={{
        modalDialog: "!max-w-2xl !w-full",
        modal: "!bg-black !bg-opacity-50",
        modalContent: "!p-0",
      }}
    >
      <div className="bg-white rounded-lg w-full max-w-2xl mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Complaints Dashboard
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab("seller")}
            className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
              activeTab === "seller"
                ? "text-red-600"
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
            style={{
              borderBottomColor:
                activeTab === "seller" ? "#E63946" : "transparent",
            }}
          >
            Seller Complaints
          </button>
          <button
            onClick={() => setActiveTab("buyer")}
            className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
              activeTab === "buyer"
                ? "text-red-600"
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
            style={{
              borderBottomColor:
                activeTab === "buyer" ? "#E63946" : "transparent",
            }}
          >
            Buyer Complaints
          </button>
        </div>

        {/* Content */}
        <div className="p-6 max-h-96 overflow-y-auto">
          {isLoading ? (
            <div className="space-y-6">
              {[...Array(3)].map((_, index) => (
                <div key={index} className="border-b border-gray-100 pb-6">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <Skeleton className="w-10 h-10 rounded-full" />
                      <div>
                        <Skeleton className="h-4 w-24 mb-1" />
                        <Skeleton className="h-3 w-16" />
                      </div>
                    </div>
                    <Skeleton className="h-4 w-20" />
                  </div>
                  <Skeleton className="h-16 w-full mb-3" />
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-3 w-20" />
                    <Skeleton className="h-6 w-16" />
                  </div>
                </div>
              ))}
            </div>
          ) : currentComplaints.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No complaints found
            </div>
          ) : (
            <div className="space-y-6">
              {currentComplaints.map((complaint: any) => (
                <div
                  key={complaint.id}
                  className="border-b border-gray-100 pb-6 last:border-b-0"
                >
                  {/* Customer Info */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-gray-600">
                          {(activeTab === "seller"
                            ? complaint.customerName
                            : complaint.sellerName || "Unknown"
                          )
                            .split(" ")
                            .map((n: string) => n[0])
                            .join("")}
                        </span>
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">
                          {activeTab === "seller"
                            ? complaint.customerName
                            : complaint.sellerName || "Unknown"}
                        </h3>
                        <p className="text-sm text-gray-500">
                          {new Date(complaint.dateFiled).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">
                        {complaint.complaintType}
                      </div>
                      <div
                        className={`text-xs px-2 py-1 rounded-full ${
                          complaint.status === "open"
                            ? "bg-red-100 text-red-800"
                            : complaint.status === "resolved"
                              ? "bg-green-100 text-green-800"
                              : "bg-yellow-100 text-yellow-800"
                        }`}
                      >
                        {complaint.status.charAt(0).toUpperCase() +
                          complaint.status.slice(1)}
                      </div>
                    </div>
                  </div>

                  {/* Complaint Text */}
                  <div className="mb-3">
                    <h4 className="font-medium text-gray-900 mb-1">
                      {complaint.subject}
                    </h4>
                    <p className="text-gray-700 text-sm leading-relaxed">
                      {complaint.description}
                    </p>
                  </div>

                  {/* Route and Package Info */}
                  <div className="bg-gray-50 p-3 rounded-lg mb-3">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Route:</span>
                        <div className="font-medium">{complaint.route}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">Package:</span>
                        <div className="font-medium">
                          {complaint.packageType}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Admin Response */}
                  {complaint.adminResponse && (
                    <div className="bg-blue-50 p-3 rounded-lg mb-3">
                      <div className="text-sm font-medium text-blue-900 mb-1">
                        Admin Response:
                      </div>
                      <div className="text-sm text-blue-800">
                        {complaint.adminResponse}
                      </div>
                    </div>
                  )}

                  {/* Order ID and Status */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      Fee: eBa${complaint.deliveryFee}
                    </span>
                    <button
                      onClick={() => handleRespond(complaint.id)}
                      className="text-sm font-medium transition-colors flex items-center"
                      style={{ color: "#0F2C59" }}
                    >
                      📧 Respond
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Pagination */}
        {currentPagination && currentPagination.totalPages > 1 && (
          <div className="flex items-center justify-between px-6 py-4 border-t border-gray-200">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={!currentPagination.hasPrev}
              className="text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              ◀ Previous
            </button>

            <div className="text-sm text-gray-600">
              Page {currentPagination.page} of {currentPagination.totalPages}
              {currentPagination.total && (
                <span className="ml-2 text-gray-400">
                  ({currentPagination.total} total)
                </span>
              )}
            </div>

            <button
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={!currentPagination.hasNext}
              className="text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next ▶
            </button>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default ComplaintsDashboardModal;
