import React, { useState } from "react";
import InteractiveButton from "./InteractiveButton/InteractiveButton";

interface ViewListingModalProps {
  isOpen: boolean;
  onClose: () => void;
  listing?: {
    id: string;
    sellerName: string;
    amount: string;
    rate: string;
    totalAmount: string;
    paymentMethod: string;
  };
}

const ViewListingModal: React.FC<ViewListingModalProps> = ({
  isOpen,
  onClose,
  listing: _listing,
}) => {
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);

  if (!isOpen) return null;

  const handleFileUpload = (file: File) => {
    setUploadedFile(file);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-[520px] max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            View Listing (Full Purchase Required)
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-xl font-normal"
          >
            ×
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* 1. Seller & Offer Overview */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <span className="text-[#0F2C59] text-lg">👤</span>
              <h3 className="text-base font-semibold text-gray-900">
                1. Seller & Offer Overview
              </h3>
            </div>

            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">
                  Seller Name / Alias
                </span>
                <span className="text-sm text-gray-900 font-medium">
                  EBA_Trader01
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Selling</span>
                <span className="text-sm text-gray-900 font-medium">
                  1,200 EBA$
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Rate</span>
                <span className="text-sm text-gray-900 font-medium">
                  $1.03 per EBA$
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">
                  Total Amount to Pay
                </span>
                <span className="text-sm text-gray-900 font-medium">
                  $1,236.00
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Payment Method(s)</span>
                <span className="text-sm text-gray-900 font-medium">
                  Bank Transfer
                </span>
              </div>
            </div>
          </div>

          {/* 2. Seller Notes & Account Details */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <span className="text-[#0F2C59] text-lg">📝</span>
              <h3 className="text-base font-semibold text-gray-900">
                2. Seller Notes & Account Details
              </h3>
            </div>

            <div className="bg-gray-50 p-3 rounded-md mb-4">
              <p className="text-sm text-gray-700 italic">
                "Please do not try to scam me by attaching fake receipts"
              </p>
            </div>

            <div className="space-y-3">
              <h4 className="text-sm font-semibold text-gray-900">
                Seller Bank Account Details
              </h4>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Bank Name</span>
                <span className="text-sm text-gray-900 font-medium">
                  First National Bank
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Account Holder</span>
                <span className="text-sm text-gray-900 font-medium">
                  John E. Trader
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Account Number</span>
                <span className="text-sm text-gray-900 font-medium">
                  ****5678
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">SWIFT/BIC Code</span>
                <span className="text-sm text-gray-900 font-medium">
                  FNBNUS33
                </span>
              </div>
            </div>
          </div>

          {/* 3. Purchase Summary */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <span className="text-[#0F2C59] text-lg">📊</span>
              <h3 className="text-base font-semibold text-gray-900">
                3. Purchase Summary (Read-Only)
              </h3>
            </div>

            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">EBA$ Amount</span>
                <span className="text-sm text-gray-900 font-medium">1,200</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Rate Per EBA$</span>
                <span className="text-sm text-gray-900 font-medium">
                  USD$1.03
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Purchase Amount</span>
                <span className="text-sm text-gray-900 font-medium">
                  USD$1,236.00
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Platform Fee</span>
                <span className="text-sm text-gray-900 font-medium">
                  USD$5.00
                </span>
              </div>
              <div className="flex justify-between border-t border-gray-200 pt-2">
                <span className="text-sm font-semibold text-gray-900">
                  Total Due
                </span>
                <span className="text-sm font-semibold text-gray-900">
                  USD$1,241.00
                </span>
              </div>
            </div>
          </div>

          {/* 4. Confirm Payment Instructions */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <span className="text-[#0F2C59] text-lg">🔒</span>
              <h3 className="text-base font-semibold text-gray-900">
                4. Confirm Payment Instructions
              </h3>
            </div>

            <div className="bg-blue-50 p-3 rounded-md mb-4">
              <div className="flex items-start space-x-2">
                <span className="text-[#0F2C59] text-sm">ℹ️</span>
                <p className="text-sm text-gray-700">
                  Contact the seller by phone, complete the transfer following
                  their instructions, then upload your receipt here.
                </p>
              </div>
            </div>

            <div className="space-y-3">
              <label className="block text-sm text-gray-700">
                Upload proof (screenshot or receipt)
              </label>

              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center ${
                  isDragOver ? "border-[#0F2C59] bg-blue-50" : "border-gray-300"
                }`}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
              >
                <div className="text-gray-400 text-4xl mb-2">📎</div>
                <p className="text-sm text-gray-600 mb-2">
                  Click to browse or drag and drop files
                </p>
                <input
                  type="file"
                  onChange={handleFileSelect}
                  className="hidden"
                  id="file-upload"
                  accept="image/*,.pdf"
                />
                <label
                  htmlFor="file-upload"
                  className="text-[#0F2C59] hover:underline cursor-pointer text-sm"
                >
                  Browse files
                </label>
              </div>
            </div>
          </div>

          {/* 5. After Buyer Marks as Paid */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <span className="text-[#16A34A] text-lg">⭕</span>
              <h3 className="text-base font-semibold text-gray-900">
                5. After Buyer Marks as Paid
              </h3>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-[#16A34A] rounded-full"></div>
                <span className="text-sm text-gray-700">
                  Seller gets notified.
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-[#16A34A] rounded-full"></div>
                <span className="text-sm text-gray-700">
                  Seller confirms receipt.
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-[#16A34A] rounded-full"></div>
                <span className="text-sm text-gray-700">
                  Platform transfers 1,200 EBA$ to the buyer's wallet.
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-[#16A34A] rounded-full"></div>
                <span className="text-sm text-gray-700">
                  Both parties can rate the transaction.
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-6 border-t border-gray-200">
          <InteractiveButton
            onClick={onClose}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
          >
            Cancel
          </InteractiveButton>

          <div className="flex items-center space-x-3">
            <InteractiveButton className="px-4 py-2 bg-[#0F2C59] text-white rounded-md hover:bg-[#0F2C59]/90 flex items-center space-x-2">
              <span>💰</span>
              <span>Pay Platform Fee ($5)</span>
            </InteractiveButton>

            <div className="flex items-center space-x-2">
              <input type="checkbox" id="have-paid" className="rounded" />
              <label htmlFor="have-paid" className="text-sm text-gray-700">
                I Have Paid
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewListingModal;
