import { useMutation, useQueryClient } from "@tanstack/react-query";
import { queryKeys } from "../queryKeys";
import { useSDK } from "@/hooks/useSDK";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";

export const useUpdateModelMutation = (
  table: string,
  role?: any,
  config?: any
) => {
  const { tdk } = useSDK({ role });
  const { showToast, tokenExpireError } = useContexts();
  const queryClient = useQueryClient();

  const mutationFn = async ({
    id,
    payload,
  }: {
    id: string | number;
    payload: Record<string, any>;
  }) => {
    const response = await tdk.update(table, id, payload);
    return response.data ?? response?.model;
  };

  return useMutation({
    mutationFn,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [(queryKeys as any)?.[table]?.all, table],
        exact: false,
        refetchType: "all",
      });
      queryClient.invalidateQueries({
        queryKey: [(queryKeys as any)?.[table]?.list, table],
        exact: false,
        refetchType: "all",
      });
      queryClient.invalidateQueries({
        queryKey: [(queryKeys as any)?.[table]?.many, table],
        exact: false,
        refetchType: "all",
      });
      queryClient.invalidateQueries({
        queryKey: [(queryKeys as any)?.[table]?.paginate, table],
        exact: false,
        refetchType: "all",
      });
      if (config?.showToast) {
        showToast("Updated successfully", 5000, ToastStatusEnum.SUCCESS);
      } else {
        console.log("Updated successfully");
      }
    },
    onError: (error) => {
      showToast(error.message, 5000, ToastStatusEnum.ERROR);
      tokenExpireError(error.message);
      console.error(error);
    },
  });
};

export const useUpdateWhereModelMutation = (
  table: string,
  role?: any,
  config?: any
) => {
  const { tdk } = useSDK({ role });
  const { showToast, tokenExpireError } = useContexts();
  const queryClient = useQueryClient();

  const mutationFn = async ({
    where,
    payload,
  }: {
    where: Record<string, any>;
    payload: Record<string, any>;
  }) => {
    const response = await tdk.updateWhere(table, where, payload);
    return response.data ?? response?.model;
  };

  return useMutation({
    mutationFn,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [(queryKeys as any)?.[table]?.all, table],
        exact: false,
        refetchType: "all",
      });
      queryClient.invalidateQueries({
        queryKey: [(queryKeys as any)?.[table]?.list, table],
        exact: false,
        refetchType: "all",
      });
      queryClient.invalidateQueries({
        queryKey: [(queryKeys as any)?.[table]?.many, table],
        exact: false,
        refetchType: "all",
      });
      queryClient.invalidateQueries({
        queryKey: [(queryKeys as any)?.[table]?.paginate, table],
        exact: false,
        refetchType: "all",
      });
      if (config?.showToast) {
        showToast("Updated successfully", 5000, ToastStatusEnum.SUCCESS);
      } else {
        console.log("Updated successfully");
      }
    },
    onError: (error) => {
      showToast(error.message, 5000, ToastStatusEnum.ERROR);
      tokenExpireError(error.message);
      console.error(error);
    },
  });
};
