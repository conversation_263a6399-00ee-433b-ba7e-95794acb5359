import { lazy } from "react";

export const AddAdminWireframeTablePage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AddAdminWireframeTablePage");
  __import.finally(() => {});
  return __import;
});

export const ViewAdminWireframeTablePage = lazy(() => {
  const __import = import("@/pages/Admin/View/ViewAdminWireframeTablePage");
  __import.finally(() => {});
  return __import;
});

export const ListAdminWireframeTablePage = lazy(() => {
  const __import = import("@/pages/Admin/List/ListAdminWireframeTablePage");
  __import.finally(() => {});
  return __import;
});

export const AdminForgotPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminForgotPage");
  __import.finally(() => {});
  return __import;
});

export const AdminLoginPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminLoginPage");
  __import.finally(() => {});
  return __import;
});

export const AdminProfilePage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminProfilePage");
  __import.finally(() => {});
  return __import;
});

export const AdminResetPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminResetPage");
  __import.finally(() => {});
  return __import;
});

export const AdminSignUpPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminSignUpPage");
  __import.finally(() => {});
  return __import;
});

export const MemberLoginPage = lazy(
  () => import("../pages/Member/Auth/MemberLoginPage")
);
export const MemberSignUpPage = lazy(
  () => import("../pages/Member/Auth/MemberSignUpPage")
);
export const MemberVerifyIdentityPage = lazy(
  () => import("../pages/Member/Auth/MemberVerifyIdentityPage")
);
export const MemberAccountPage = lazy(
  () => import("../pages/Member/Auth/MemberAccountPage")
);
export const MemberProfilePage = lazy(
  () => import("../pages/Member/Auth/MemberProfilePage")
);
export const MemberAddListingPage = lazy(
  () => import("../pages/Member/Add/MemberAddListingPage")
);
export const MemberListingsListPage = lazy(
  () => import("../pages/Member/List/MemberListingsListPage")
);
export const MemberRewardsListPage = lazy(
  () => import("../pages/Member/List/MemberRewardsListPage")
);
export const MemberInboxPage = lazy(
  () => import("../pages/Member/Custom/MemberInboxPage")
);

export const AdminDashboardPage = lazy(() => {
  const __import = import("@/pages/Admin/Dashboard/AdminDashboardPage");
  __import.finally(() => {});
  return __import;
});

export const LandingPage = lazy(() => {
  const __import = import("@/pages/Admin/View/LandingPage");
  __import.finally(() => {});
  return __import;
});

export const MemberLandingPage = lazy(() => {
  const __import = import("@/pages/Member/LandingPage.tsx");
  __import.finally(() => {});
  return __import;
});

export const MagicLoginVerifyPage = lazy(() => {
  const __import = import("@/pages/MagicLogin/MagicLoginVerifyPage");
  __import.finally(() => {});
  return __import;
});

export const MemberMagicLoginPage = lazy(() => {
  const __import = import("@/pages/MagicLogin/UserMagicLoginPage");
  __import.finally(() => {});
  return __import;
});

export const MemberDashboardPage = lazy(() => {
  const __import = import("@/pages/Member/Dashboard/Dashboard.tsx");
  __import.finally(() => {});
  return __import;
});

// Member Marketplace Pages
export const MemberMarketplaceListPage = lazy(() => {
  const __import = import("@/pages/Member/List/MemberMarketplaceListPage.tsx");
  __import.finally(() => {});
  return __import;
});

export const MemberMarketplaceDetailPage = lazy(() => {
  const __import = import(
    "@/pages/Member/View/MemberMarketplaceDetailPage.tsx"
  );
  __import.finally(() => {});
  return __import;
});

// Member Listings Pages

export const MemberEditListingPage = lazy(() => {
  const __import = import("@/pages/Member/Edit/MemberEditListingPage.tsx");
  __import.finally(() => {});
  return __import;
});

export const MemberViewListingPage = lazy(() => {
  const __import = import("@/pages/Member/View/MemberViewListingPage.tsx");
  __import.finally(() => {});
  return __import;
});

// Member Transactions Pages
export const MemberTransactionsListPage = lazy(() => {
  const __import = import("@/pages/Member/List/MemberTransactionsListPage.tsx");
  __import.finally(() => {});
  return __import;
});

export const MemberViewTransactionPage = lazy(() => {
  const __import = import(
    "@/pages/Member/View/MemberTransactionDetailsPage.tsx"
  );
  __import.finally(() => {});
  return __import;
});

// Member Rewards Pages

export const MemberAvailableDeliveriesPage = lazy(() => {
  const __import = import(
    "@/pages/Member/List/MemberAvailableDeliveriesPage.tsx"
  );
  __import.finally(() => {});
  return __import;
});

export const MemberMyDeliveriesPage = lazy(() => {
  const __import = import("@/pages/Member/List/MemberMyDeliveriesPage.tsx");
  __import.finally(() => {});
  return __import;
});

// Member Account Pages

export const MemberDeliverySettingsPage = lazy(() => {
  const __import = import("@/pages/Member/Auth/MemberDeliverySettingsPage.tsx");
  __import.finally(() => {});
  return __import;
});

export const AdminAddUserPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddUserPage");
  __import.finally(() => {});
  return __import;
});

export const AdminUsersListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminUsersListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminCategoriesListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminCategoriesListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddCategoryPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddCategoryPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditCategoryPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditCategoryPage");
  __import.finally(() => {});
  return __import;
});

export const AdminListingsListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListingsListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminTopUpRequestsListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminTopUpRequestsListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminTransactionsListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminTransactionsListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminDisputesAndRefundsListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminDisputesAndRefundsListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminDeliveryApplicationsListPage = lazy(() => {
  const __import = import(
    "@/pages/Admin/List/AdminDeliveryApplicationsListPage"
  );
  __import.finally(() => {});
  return __import;
});

export const AdminRewardsAndReferralsListPage = lazy(() => {
  const __import = import(
    "@/pages/Admin/List/AdminRewardsAndReferralsListPage"
  );
  __import.finally(() => {});
  return __import;
});

export const AdminViewTransactionPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewTransactionPage");
  __import.finally(() => {});
  return __import;
});

export const AdminPromotionsAndSponsorshipsListPage = lazy(() => {
  const __import = import(
    "@/pages/Admin/List/AdminPromotionsAndSponsorshipsListPage"
  );
  __import.finally(() => {});
  return __import;
});

export const AdminPlatformSettingsListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminPlatformSettingsListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminReportedListingListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminReportedListingListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewListingPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewListingPage");
  __import.finally(() => {});
  return __import;
});

export const AdminDeliveryAgentComplaintsListPage = lazy(() => {
  const __import = import(
    "@/pages/Admin/List/AdminDeliveryAgentComplaintsListPage"
  );
  __import.finally(() => {});
  return __import;
});

export const AdminDeliveryAgentComplaintDetailsPage = lazy(() => {
  const __import = import(
    "@/pages/Admin/View/AdminDeliveryAgentComplaintDetailsPage"
  );
  __import.finally(() => {});
  return __import;
});

// OTHERS

export const TestComponents = lazy(() => {
  const __import = import("@/pages/PG/Custom/TestComponents");
  __import.finally(() => {});
  return __import;
});
