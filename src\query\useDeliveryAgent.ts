import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useSDK } from "../hooks/useSDK";
import { useToast } from "../components/Toast";
import { ToastStatusEnum } from "../utils/Enums";

// Interfaces
export interface IDeliveryAssignmentFilters {
  page?: number;
  limit?: number;
  status?: "active" | "completed" | "all";
  search?: string;
}

export interface IDeliveryLog {
  id: number;
  type: string;
  message: string;
  status: string;
  metadata?: any;
  loggedBy: string;
  createdAt: string;
}

export interface IDeliveryAssignment {
  id: number;
  deliveryId: string;
  taskId: number;
  status: string;
  
  // Package information
  packageType: string;
  packageDescription: string;
  packageWeight?: number;
  packageDimensions?: string;
  
  // Pickup information
  pickupFrom: string;
  pickupLocation: string;
  pickupAddress: string;
  pickupCity: string;
  pickupProvince: string;
  pickupPostalCode?: string;
  pickupContactName?: string;
  pickupContactPhone?: string;
  
  // Delivery information
  deliverTo: string;
  deliverLocation: string;
  dropoffAddress: string;
  dropoffCity: string;
  dropoffProvince: string;
  dropoffPostalCode?: string;
  dropoffContactName?: string;
  dropoffContactPhone?: string;
  
  // Fee and delivery details
  fee: string;
  deliveryFee: number;
  currency: string;
  deliveryDeadline?: string;
  estimatedDistance?: number;
  specialInstructions?: string;
  requiresFragileHandling: boolean;
  requiresSignature: boolean;
  
  // Status timestamps
  assignedAt: string;
  acceptedAt?: string;
  pickedUpAt?: string;
  deliveredAt?: string;
  cancelledAt?: string;
  
  // Additional details
  agentNotes?: string;
  deliveryProofUrl?: string;
  recipientSignatureUrl?: string;
  confirmationCode?: string;
  createdAt: string;
  
  // Logs (only in detailed view)
  logs?: IDeliveryLog[];
}

// Delivery Assignments Query
export const useDeliveryAssignmentsQuery = (filters: IDeliveryAssignmentFilters = {}) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["delivery-assignments", filters],
    queryFn: async () => {
      console.log("🚀 Fetching delivery assignments with filters:", filters);

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/delivery-agent/assignments",
          method: "GET",
          params: filters,
        });
        console.log("📡 Delivery Assignments API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Delivery Assignments API Call Error:", error);
        throw error;
      }
    },
    staleTime: 30 * 1000, // 30 seconds
    cacheTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Single Assignment Details Query
export const useDeliveryAssignmentQuery = (assignmentId: number) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["delivery-assignment", assignmentId],
    queryFn: async () => {
      console.log("🚀 Fetching delivery assignment details:", assignmentId);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/delivery-agent/assignments/${assignmentId}`,
          method: "GET",
        });
        console.log("📡 Delivery Assignment Details API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Delivery Assignment Details API Call Error:", error);
        throw error;
      }
    },
    enabled: !!assignmentId && assignmentId > 0,
    staleTime: 30 * 1000, // 30 seconds
    cacheTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Update Delivery Status Mutation
export const useUpdateDeliveryStatusMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { success, error } = useToast();

  return useMutation({
    mutationFn: async ({ 
      assignmentId, 
      status, 
      notes, 
      confirmationCode 
    }: {
      assignmentId: number;
      status: "accepted" | "picked_up" | "delivered" | "cancelled";
      notes?: string;
      confirmationCode?: string;
    }) => {
      console.log("🚀 Updating delivery status:", { assignmentId, status, notes, confirmationCode });

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/delivery-agent/assignments/${assignmentId}/status`,
          method: "POST",
          data: { status, notes, confirmationCode },
        });
        console.log("📡 Update Delivery Status API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Update Delivery Status API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (data, variables) => {
      console.log("✅ Successfully updated delivery status:", data);
      const statusMessages = {
        accepted: "Delivery accepted successfully",
        picked_up: "Package marked as picked up",
        delivered: "Package marked as delivered",
        cancelled: "Delivery cancelled successfully",
      };
      success(statusMessages[variables.status], ToastStatusEnum.SUCCESS);
      
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ["delivery-assignments"] });
      queryClient.invalidateQueries({ queryKey: ["delivery-assignment", variables.assignmentId] });
    },
    onError: (error: any) => {
      console.error("❌ Update delivery status failed:", error);
      const errorMessage = error?.response?.data?.message || "Failed to update delivery status";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};

// Add Delivery Log Mutation
export const useAddDeliveryLogMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { success, error } = useToast();

  return useMutation({
    mutationFn: async ({ 
      assignmentId, 
      logType, 
      message, 
      metadata 
    }: {
      assignmentId: number;
      logType: string;
      message: string;
      metadata?: any;
    }) => {
      console.log("🚀 Adding delivery log:", { assignmentId, logType, message, metadata });

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/delivery-agent/assignments/${assignmentId}/logs`,
          method: "POST",
          data: { logType, message, metadata },
        });
        console.log("📡 Add Delivery Log API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Add Delivery Log API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (data, variables) => {
      console.log("✅ Successfully added delivery log:", data);
      success("Log entry added successfully", ToastStatusEnum.SUCCESS);
      
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ["delivery-assignment", variables.assignmentId] });
    },
    onError: (error: any) => {
      console.error("❌ Add delivery log failed:", error);
      const errorMessage = error?.response?.data?.message || "Failed to add log entry";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};

// Update Confirmation Code Mutation
export const useUpdateConfirmationCodeMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { success, error } = useToast();

  return useMutation({
    mutationFn: async ({ 
      assignmentId, 
      confirmationCode 
    }: {
      assignmentId: number;
      confirmationCode: string;
    }) => {
      console.log("🚀 Updating confirmation code:", { assignmentId, confirmationCode });

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/delivery-agent/assignments/${assignmentId}/confirmation`,
          method: "POST",
          data: { confirmationCode },
        });
        console.log("📡 Update Confirmation Code API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Update Confirmation Code API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (data, variables) => {
      console.log("✅ Successfully updated confirmation code:", data);
      success("Confirmation code updated successfully", ToastStatusEnum.SUCCESS);
      
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ["delivery-assignment", variables.assignmentId] });
      queryClient.invalidateQueries({ queryKey: ["delivery-assignments"] });
    },
    onError: (error: any) => {
      console.error("❌ Update confirmation code failed:", error);
      const errorMessage = error?.response?.data?.message || "Failed to update confirmation code";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};
