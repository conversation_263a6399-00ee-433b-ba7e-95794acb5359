import * as yup from "yup";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { InteractiveButton } from "@/components/InteractiveButton";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum, RoleEnum } from "@/utils/Enums";
import { LazyLoad } from "@/components/LazyLoad";
import { useSDK } from "@/hooks/useSDK";
import { MkdPasswordInput } from "@/components/MkdPasswordInput";
import MkdInputV2 from "@/components/MkdInputV2";

interface MemberLoginPageProps {
  role?: string;
}

const MemberLoginPage = ({ role = RoleEnum.USER }: MemberLoginPageProps) => {
  const { sdk } = useSDK();
  const { authDispatch: dispatch, showToast } = useContexts();

  const [submitLoading, setSubmitLoading] = useState(false);
  const location = useLocation();

  const searchParams = new URLSearchParams(location.search);
  const redirect_uri = searchParams.get("redirect_uri");

  const navigate = useNavigate();

  const schema = yup
    .object({
      email: yup.string().email().required(),
      password: yup.string().required(),
    })
    .required();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const onSubmit = async (data: yup.InferType<typeof schema>) => {
    try {
      setSubmitLoading(true);
      const result = await sdk.login(data.email, data.password, role);

      console.log("result", result);
      if (!result.error) {
        dispatch({
          type: "LOGIN",
          payload: result as any,
        });
        showToast("Successfully Logged In", 4000, ToastStatusEnum.SUCCESS);
        navigate(redirect_uri ?? `/member/dashboard`);
      } else {
        setSubmitLoading(false);
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field as "email" | "password", {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error: any) {
      setSubmitLoading(false);
      showToast(
        error?.response?.data?.message
          ? error?.response?.data?.message
          : error?.message,
        4000,
        ToastStatusEnum.ERROR
      );
      setError("email", {
        type: "manual",
        message: error?.response?.data?.message
          ? error?.response?.data?.message
          : error?.message,
      });
    }
  };

  return (
    <main
      className="flex items-center justify-center min-h-screen"
      style={{ backgroundColor: "#0F2C59" }}
    >
      <div className="w-full max-w-md p-8 space-y-6 bg-white rounded-2xl shadow-lg">
        <div className="flex flex-col items-center text-center">
          <div className="flex items-center gap-4">
            <h1 className="text-5xl font-extrabold tracking-tighter">
              eBaDollar
            </h1>
          </div>
          <p className="mt-4 text-base text-gray-500">
            Welcome! Please sign in to your account
          </p>
        </div>

        <form className="w-full space-y-6" onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-2">
            <LazyLoad>
              <MkdInputV2
                name="email"
                type="email"
                register={register}
                errors={errors}
                required
                placeholder="<EMAIL>"
              >
                <MkdInputV2.Container>
                  <MkdInputV2.Label className="text-sm font-semibold !text-[#374151]">
                    Email Address
                  </MkdInputV2.Label>
                  <MkdInputV2.Field className="!bg-gray-100 !border-gray-200 !text-[#374151]" />
                  <MkdInputV2.Error />
                </MkdInputV2.Container>
              </MkdInputV2>
            </LazyLoad>
          </div>
          <div className="space-y-2">
            <LazyLoad>
              <MkdPasswordInput
                required
                name="password"
                label="Password"
                errors={errors}
                register={register}
                inputClassName="!bg-gray-100 !border-gray-200 !text-[#374151]"
                placeholder="************"
                labelClassName="text-sm font-semibold !text-[#374151]"
              />
            </LazyLoad>
          </div>
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                className="w-4 h-4 bg-gray-100 border-gray-300 rounded text-[#E63946] focus:ring-[#E63946]"
              />
              <label
                htmlFor="remember-me"
                className="block ml-2 font-medium text-[#374151]"
              >
                Remember me
              </label>
            </div>
            <Link
              to={`/member/forgot`}
              className="font-medium !text-[#E63946] hover:opacity-80"
            >
              Forgot password?
            </Link>
          </div>
          <InteractiveButton
            type="submit"
            className="flex w-full items-center justify-center rounded-md bg-[#E63946] py-3 font-semibold tracking-wide text-white outline-none focus:outline-none"
            loading={submitLoading}
            disabled={submitLoading}
          >
            Sign In
          </InteractiveButton>
        </form>

        <p className="text-sm text-center text-gray-500">
          Don't have an account?{" "}
          <Link
            to="/member/sign-up"
            className="font-medium text-[#E63946] hover:opacity-80"
          >
            Sign Up
          </Link>
        </p>
      </div>
    </main>
  );
};

export default MemberLoginPage;
