import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useSDK } from "../hooks/useSDK";
import { useToast } from "../hooks/useToast";
import { ToastStatusEnum } from "../utils/Enums";

// Interface definitions
interface IListing {
  id: number;
  name: string;
  description: string;
  price: number;
  discountPrice?: number;
  quantity: number;
  type: string;
  category: string;
  status: string;
  images: string[];
  image: string;
  createdAt: string;
  updatedAt: string;
  sold_date: string;
  delivery_status: string;
  buyer?: string;
  viewCount: number;
  inquiryCount: number;
  offerCount: number;
  favoriteCount: number;
  pendingOffers: number;
}

interface IListingOffer {
  id: number;
  buyerName: string;
  buyerEmail: string;
  buyerAvatar: string;
  date: string;
  status: string;
  amount: string;
  quantity: number;
  message: string;
  expiresAt?: string;
  respondedAt?: string;
  createdAt: string;
  updatedAt: string;
}

interface IListingPromotion {
  id: number;
  listingId: number;
  listingName: string;
  listingPrice: number;
  listingImage?: string;
  startDate: string;
  endDate: string;
  duration: number;
  cost: number;
  paymentMethod: string;
  status: string;
  createdAt: string;
  isActive: boolean;
  daysRemaining: number;
}

interface IPromotionSettings {
  costPerDay: {
    eba: number;
    usd: number;
  };
  totalCost: {
    eba: number;
    usd: number;
  };
  maxDuration: number;
  maxActivePromotions: number;
  requestedDays: number;
}

interface IListingFilters {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  type?: string;
  category?: string;
  dateAdded?: string;
  sortBy?: string;
  minPrice?: number;
  maxPrice?: number;
  tab?: string;
}

// Hook to fetch member listings with enhanced filtering and statistics
export const useMemberListingsQuery = (filters: IListingFilters = {}) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["member-listings", filters],
    queryFn: async () => {
      console.log("🚀 Fetching member listings with filters:", filters);

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/listings",
          method: "GET",
          params: filters,
        });
        console.log("📡 Member Listings API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Member Listings API Call Error:", error);
        throw error;
      }
    },
    staleTime: 30 * 1000, // 30 seconds
    cacheTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Hook to fetch listing statistics
export const useListingStatisticsQuery = (listingId: number) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["listing-statistics", listingId],
    queryFn: async () => {
      console.log("🚀 Fetching listing statistics for ID:", listingId);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/listings/${listingId}/statistics`,
          method: "GET",
        });
        console.log("📡 Listing Statistics API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Listing Statistics API Call Error:", error);
        throw error;
      }
    },
    enabled: !!listingId,
    staleTime: 60 * 1000, // 1 minute
    cacheTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook to fetch offers for a listing
export const useListingOffersQuery = (
  listingId: number,
  filters: { page?: number; limit?: number; status?: string } = {}
) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["listing-offers", listingId, filters],
    queryFn: async () => {
      console.log(
        "🚀 Fetching listing offers for ID:",
        listingId,
        "with filters:",
        filters
      );

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/listings/${listingId}/offers`,
          method: "GET",
          params: filters,
        });
        console.log("📡 Listing Offers API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Listing Offers API Call Error:", error);
        throw error;
      }
    },
    enabled: !!listingId,
    staleTime: 30 * 1000, // 30 seconds
    cacheTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Hook to accept an offer
export const useAcceptOfferMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: async (offerId: number) => {
      console.log("🚀 Accepting offer with ID:", offerId);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/listings/offers/${offerId}/accept`,
          method: "POST",
        });
        console.log("📡 Accept Offer API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Accept Offer API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (res: any) => {
      console.log("✅ Accept Offer Mutation Success:", res);
      if (!res.error) {
        showToast(
          "Offer accepted successfully!",
          5000,
          ToastStatusEnum.SUCCESS
        );
        // Invalidate related queries
        queryClient.invalidateQueries({ queryKey: ["listing-offers"] });
        queryClient.invalidateQueries({ queryKey: ["member-listings"] });
        queryClient.invalidateQueries({ queryKey: ["listing-statistics"] });
      } else {
        console.error("❌ API returned error:", res.message);
        showToast(
          res.message || "Failed to accept offer",
          5000,
          ToastStatusEnum.ERROR
        );
      }
    },
    onError: (err: any) => {
      console.error("❌ Accept Offer Mutation Error:", err);
      showToast(
        err.message || "An error occurred while accepting the offer",
        5000,
        ToastStatusEnum.ERROR
      );
    },
  });
};

// Hook to reject an offer
export const useRejectOfferMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: async (offerId: number) => {
      console.log("🚀 Rejecting offer with ID:", offerId);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/listings/offers/${offerId}/reject`,
          method: "POST",
        });
        console.log("📡 Reject Offer API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Reject Offer API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (res: any) => {
      console.log("✅ Reject Offer Mutation Success:", res);
      if (!res.error) {
        showToast("Offer rejected successfully", 5000, ToastStatusEnum.SUCCESS);
        // Invalidate related queries
        queryClient.invalidateQueries({ queryKey: ["listing-offers"] });
        queryClient.invalidateQueries({ queryKey: ["listing-statistics"] });
      } else {
        console.error("❌ API returned error:", res.message);
        showToast(
          res.message || "Failed to reject offer",
          5000,
          ToastStatusEnum.ERROR
        );
      }
    },
    onError: (err: any) => {
      console.error("❌ Reject Offer Mutation Error:", err);
      showToast(
        err.message || "An error occurred while rejecting the offer",
        5000,
        ToastStatusEnum.ERROR
      );
    },
  });
};

// Hook to create a new offer
export const useCreateOfferMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: async (offerData: {
      listingId: number;
      offerAmount: number;
      quantity?: number;
      message?: string;
    }) => {
      console.log("🚀 Creating offer with data:", offerData);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/listings/${offerData.listingId}/offers`,
          method: "POST",
          body: {
            offerAmount: offerData.offerAmount,
            quantity: offerData.quantity || 1,
            message: offerData.message || "",
          },
        });
        console.log("📡 Create Offer API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Create Offer API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (res: any) => {
      console.log("✅ Create Offer Mutation Success:", res);
      if (!res.error) {
        showToast(
          "Offer submitted successfully!",
          5000,
          ToastStatusEnum.SUCCESS
        );
        // Invalidate related queries
        queryClient.invalidateQueries({ queryKey: ["listing-offers"] });
        queryClient.invalidateQueries({ queryKey: ["listing-statistics"] });
      } else {
        console.error("❌ API returned error:", res.message);
        showToast(
          res.message || "Failed to create offer",
          5000,
          ToastStatusEnum.ERROR
        );
      }
    },
    onError: (err: any) => {
      console.error("❌ Create Offer Mutation Error:", err);
      showToast(
        err.message || "An error occurred while creating the offer",
        5000,
        ToastStatusEnum.ERROR
      );
    },
  });
};

// Hook to get promotion settings
export const usePromotionSettingsQuery = (days: number = 1) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["promotion-settings", days],
    queryFn: async () => {
      console.log("🚀 Fetching promotion settings for days:", days);

      try {
        const response = await sdk.request({
          endpoint:
            "/v2/api/ebadollar/custom/member/listings/promotion/settings",
          method: "GET",
          params: { days },
        });
        console.log("📡 Promotion Settings API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Promotion Settings API Call Error:", error);
        throw error;
      }
    },
    enabled: days > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Hook to promote a listing
export const usePromoteListingMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: async (promotionData: {
      listingId: number;
      days: number;
      paymentMethod?: string;
    }) => {
      console.log("🚀 Promoting listing with data:", promotionData);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/listings/${promotionData.listingId}/promote`,
          method: "POST",
          body: {
            days: promotionData.days,
            paymentMethod: promotionData.paymentMethod || "eba",
          },
        });
        console.log("📡 Promote Listing API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Promote Listing API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (res: any) => {
      console.log("✅ Promote Listing Mutation Success:", res);
      if (!res.error) {
        showToast(
          "Listing promoted successfully!",
          5000,
          ToastStatusEnum.SUCCESS
        );
        // Invalidate related queries
        queryClient.invalidateQueries({ queryKey: ["member-listings"] });
        queryClient.invalidateQueries({ queryKey: ["listing-promotions"] });
      } else {
        console.error("❌ API returned error:", res.message);
        showToast(
          res.message || "Failed to promote listing",
          5000,
          ToastStatusEnum.ERROR
        );
      }
    },
    onError: (err: any) => {
      console.error("❌ Promote Listing Mutation Error:", err);
      showToast(
        err.message || "An error occurred while promoting the listing",
        5000,
        ToastStatusEnum.ERROR
      );
    },
  });
};

// Hook to fetch user's promotions
export const useListingPromotionsQuery = (
  filters: { page?: number; limit?: number; status?: string } = {}
) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["listing-promotions", filters],
    queryFn: async () => {
      console.log("🚀 Fetching listing promotions with filters:", filters);

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/listings/promotions",
          method: "GET",
          params: filters,
        });
        console.log("📡 Listing Promotions API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Listing Promotions API Call Error:", error);
        throw error;
      }
    },
    staleTime: 60 * 1000, // 1 minute
    cacheTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook to cancel a promotion
export const useCancelPromotionMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: async (promotionId: number) => {
      console.log("🚀 Cancelling promotion with ID:", promotionId);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/listings/promotions/${promotionId}/cancel`,
          method: "POST",
        });
        console.log("📡 Cancel Promotion API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Cancel Promotion API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (res: any) => {
      console.log("✅ Cancel Promotion Mutation Success:", res);
      if (!res.error) {
        showToast(
          "Promotion cancelled successfully",
          5000,
          ToastStatusEnum.SUCCESS
        );
        // Invalidate related queries
        queryClient.invalidateQueries({ queryKey: ["listing-promotions"] });
        queryClient.invalidateQueries({ queryKey: ["member-listings"] });
      } else {
        console.error("❌ API returned error:", res.message);
        showToast(
          res.message || "Failed to cancel promotion",
          5000,
          ToastStatusEnum.ERROR
        );
      }
    },
    onError: (err: any) => {
      console.error("❌ Cancel Promotion Mutation Error:", err);
      showToast(
        err.message || "An error occurred while cancelling the promotion",
        5000,
        ToastStatusEnum.ERROR
      );
    },
  });
};

export type {
  IListing,
  IListingOffer,
  IListingPromotion,
  IPromotionSettings,
  IListingFilters,
};
