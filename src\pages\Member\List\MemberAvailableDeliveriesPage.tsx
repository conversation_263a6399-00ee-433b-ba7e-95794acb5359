import React, { useState } from "react";
import { MemberWrapper } from "../../../components/MemberWrapper";
import { InteractiveButton } from "../../../components/InteractiveButton";
import ComplaintsDashboardModal from "../../../components/ComplaintsDashboardModal";
import { MkdLoader } from "../../../components/MkdLoader";
import { Skeleton } from "../../../components/Skeleton";
import {
  useAvailableDeliveryTasksQuery,
  useAcceptDeliveryTaskMutation,
  useDeliveryAgentStatsQuery,
  usePackageTypesQuery,
  IDeliveryTaskFilters,
} from "../../../query/useDeliveryTasks";

interface DeliveryTask {
  id: number;
  type: string;
  price: string;
  postedTime: string;
  pickupLocation: string;
  dropoffLocation: string;
  estimatedDistance: string;
  deliveryDeadline: string;
}

const MemberAvailableDeliveriesPage = () => {
  const [isComplaintsModalOpen, setIsComplaintsModalOpen] = useState(false);
  const [filters, setFilters] = useState<IDeliveryTaskFilters>({
    page: 1,
    limit: 12,
    sort_by: "created_at",
    sort_order: "desc",
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedPackageType, setSelectedPackageType] = useState("");
  const [priceRange, setPriceRange] = useState({ min: "", max: "" });
  const [maxDistance, setMaxDistance] = useState("");
  const [locationFilter, setLocationFilter] = useState("All Locations");

  // Query hooks
  const {
    data: tasksData,
    isLoading: tasksLoading,
    error: tasksError,
  } = useAvailableDeliveryTasksQuery(filters);
  const { data: statsData, isLoading: statsLoading } =
    useDeliveryAgentStatsQuery();
  const { data: packageTypesData } = usePackageTypesQuery();
  const { mutate: acceptTask, isPending: isAccepting } =
    useAcceptDeliveryTaskMutation();

  // Filter handlers
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setFilters((prev) => ({ ...prev, search: value, page: 1 }));
  };

  const handlePackageTypeFilter = (value: string) => {
    setSelectedPackageType(value);
    setFilters((prev) => ({
      ...prev,
      package_type: value === "All Types" ? "" : value,
      page: 1,
    }));
  };

  const handlePriceRangeFilter = (min: string, max: string) => {
    setPriceRange({ min, max });
    setFilters((prev) => ({
      ...prev,
      min_price: min ? parseFloat(min) : undefined,
      max_price: max ? parseFloat(max) : undefined,
      page: 1,
    }));
  };

  const handleDistanceFilter = (value: string) => {
    setMaxDistance(value);
    setFilters((prev) => ({
      ...prev,
      max_distance: value ? parseFloat(value) : undefined,
      page: 1,
    }));
  };

  const handleAcceptDelivery = (taskId: number) => {
    acceptTask(taskId);
  };

  // Get data from queries
  const deliveryTasks = tasksData?.data || [];
  const agentStats = statsData?.data || {
    totalDeliveries: 0,
    completedDeliveries: 0,
    successRate: "0.00",
    averageRating: "0.00",
    totalEarnings: "0.00",
  };
  const packageTypes = packageTypesData?.data || [];

  return (
    <MemberWrapper>
      <div className="h-full bg-[#0F2C59] overflow-auto">
        <div className="p-8">
          {/* Header Section */}
          <div className="mb-8">
            <h1 className="text-4xl font-bold text-white mb-6">
              Available Delivery Tasks
            </h1>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
              <div className="bg-white rounded-lg p-6 text-center">
                <div className="flex items-center justify-center mb-3">
                  <span className="text-2xl mr-2">📦</span>
                </div>
                {statsLoading ? (
                  <Skeleton className="h-10 w-16 mx-auto mb-1" />
                ) : (
                  <div className="text-4xl font-bold text-gray-800 mb-1">
                    {agentStats.totalDeliveries}
                  </div>
                )}
                <div className="text-gray-600 text-sm">Total Deliveries</div>
              </div>

              <div className="bg-white rounded-lg p-6 text-center">
                <div className="flex items-center justify-center mb-3">
                  <span className="text-2xl mr-2">✅</span>
                </div>
                {statsLoading ? (
                  <Skeleton className="h-10 w-16 mx-auto mb-1" />
                ) : (
                  <div className="text-4xl font-bold text-gray-800 mb-1">
                    {agentStats.successRate}%
                  </div>
                )}
                <div className="text-gray-600 text-sm">Success Rate</div>
              </div>

              <div className="bg-white rounded-lg p-6 text-center">
                <div className="flex items-center justify-center mb-3">
                  <span className="text-2xl mr-2">⭐</span>
                </div>
                {statsLoading ? (
                  <Skeleton className="h-10 w-16 mx-auto mb-1" />
                ) : (
                  <div className="text-4xl font-bold text-gray-800 mb-1">
                    {agentStats.averageRating}
                  </div>
                )}
                <div className="text-gray-600 text-sm">Average Rating</div>
              </div>

              <div className="bg-white rounded-lg p-6 text-center">
                <div className="flex items-center justify-center mb-3">
                  <span className="text-2xl mr-2">💰</span>
                </div>
                {statsLoading ? (
                  <Skeleton className="h-10 w-20 mx-auto mb-1" />
                ) : (
                  <div className="text-4xl font-bold text-gray-800 mb-1">
                    eBa${agentStats.totalEarnings}
                  </div>
                )}
                <div className="text-gray-600 text-sm">Total Earnings</div>
              </div>
            </div>

            {/* View All Complaints Link */}
            <div className="mb-8 text-right">
              <button
                onClick={() => setIsComplaintsModalOpen(true)}
                className="text-white hover:text-gray-300 text-sm font-medium"
              >
                View All Complaints →
              </button>
            </div>

            {/* Filters Section */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
              <div>
                <label className="block text-white text-sm font-medium mb-2">
                  Location
                </label>
                <select
                  value={locationFilter}
                  onChange={(e) => setLocationFilter(e.target.value)}
                  className="w-full px-3 py-2.5 bg-white border border-gray-300 rounded-md text-gray-900 text-sm focus:outline-none focus:ring-2 focus:ring-[#E63946] focus:border-[#E63946]"
                >
                  <option>All Locations</option>
                  <option>Toronto, ON</option>
                  <option>Vancouver, BC</option>
                  <option>Montreal, QC</option>
                  <option>Calgary, AB</option>
                  <option>Ottawa, ON</option>
                  <option>Edmonton, AB</option>
                </select>
              </div>

              <div>
                <label className="block text-white text-sm font-medium mb-2">
                  Package Type
                </label>
                <select
                  value={selectedPackageType}
                  onChange={(e) => handlePackageTypeFilter(e.target.value)}
                  className="w-full px-3 py-2.5 bg-white border border-gray-300 rounded-md text-gray-900 text-sm focus:outline-none focus:ring-2 focus:ring-[#E63946] focus:border-[#E63946]"
                >
                  <option value="">All Types</option>
                  {packageTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label} ({type.count})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-white text-sm font-medium mb-2">
                  Delivery Deadline
                </label>
                <select
                  value={deliveryDeadlineFilter}
                  onChange={(e) => setDeliveryDeadlineFilter(e.target.value)}
                  className="w-full px-3 py-2.5 bg-white border border-gray-300 rounded-md text-gray-900 text-sm focus:outline-none focus:ring-2 focus:ring-[#E63946] focus:border-[#E63946]"
                >
                  <option>Any Time</option>
                  <option>Within 6 hours</option>
                  <option>Within 12 hours</option>
                  <option>Within 24 hours</option>
                </select>
              </div>

              <div>
                <label className="block text-white text-sm font-medium mb-2">
                  Search
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => handleSearch(e.target.value)}
                    placeholder="Search deliveries..."
                    className="w-full px-3 py-2.5 pl-10 bg-white border border-gray-300 rounded-md text-gray-900 text-sm focus:outline-none focus:ring-2 focus:ring-[#E63946] focus:border-[#E63946]"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg
                      className="h-4 w-4 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            {/* Delivery Tasks Grid */}
            {tasksLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(6)].map((_, index) => (
                  <div
                    key={index}
                    className="bg-white rounded-lg p-5 shadow-md"
                  >
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <Skeleton className="h-6 w-24 mb-2" />
                        <Skeleton className="h-4 w-20" />
                      </div>
                      <div className="text-right">
                        <Skeleton className="h-6 w-16 mb-1" />
                        <Skeleton className="h-3 w-12" />
                      </div>
                    </div>
                    <div className="space-y-3 mb-4">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                    </div>
                    <Skeleton className="h-10 w-full" />
                  </div>
                ))}
              </div>
            ) : tasksError ? (
              <div className="text-center py-10 text-white">
                <p className="text-lg mb-4">Failed to load delivery tasks</p>
                <button
                  onClick={() => window.location.reload()}
                  className="bg-[#E63946] text-white px-4 py-2 rounded-md hover:bg-red-700"
                >
                  Retry
                </button>
              </div>
            ) : deliveryTasks.length === 0 ? (
              <div className="text-center py-10 text-white">
                <p className="text-lg">
                  No delivery tasks available at the moment
                </p>
                <p className="text-sm text-gray-300 mt-2">
                  Check back later for new opportunities
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {deliveryTasks.map((task) => (
                  <div
                    key={task.id}
                    className="bg-white rounded-lg p-5 shadow-md"
                  >
                    {/* Task Header */}
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-800 mb-1">
                          {task.type}
                        </h3>
                        <p className="text-sm text-gray-600">
                          Posted {task.postedTime}
                        </p>
                      </div>
                      <div className="text-right">
                        <div
                          className="text-lg font-bold"
                          style={{ color: "#E63946" }}
                        >
                          eBa$ {task.price}
                        </div>
                        <div className="text-xs text-gray-600">On Time</div>
                      </div>
                    </div>

                    {/* Location Info */}
                    <div className="space-y-3 mb-5">
                      <div className="flex items-center">
                        <div className="w-4 h-4 mr-3 flex-shrink-0">
                          <svg
                            className="w-4 h-4 text-gray-500"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-700">
                            Pickup Location
                          </div>
                          <div className="text-sm text-gray-600">
                            {task.pickupLocation}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center">
                        <div className="w-4 h-4 mr-3 flex-shrink-0">
                          <svg
                            className="w-4 h-4 text-gray-500"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-700">
                            Dropoff Location
                          </div>
                          <div className="text-sm text-gray-600">
                            {task.dropoffLocation}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center">
                        <div className="w-4 h-4 mr-3 flex-shrink-0">
                          <svg
                            className="w-4 h-4 text-gray-500"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
                            <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z" />
                          </svg>
                        </div>
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-700">
                            Estimated Distance
                          </div>
                          <div className="text-sm text-gray-600">
                            {task.estimatedDistance}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center">
                        <div className="w-4 h-4 mr-3 flex-shrink-0">
                          <svg
                            className="w-4 h-4 text-gray-500"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-700">
                            Delivery Deadline
                          </div>
                          <div className="text-sm text-gray-600">
                            {task.deliveryDeadline}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Accept Button */}
                    <InteractiveButton
                      onClick={() => handleAcceptDelivery(task.id)}
                      disabled={isAccepting}
                      className="w-full py-3 text-white font-medium rounded-md bg-[#0F2C59] hover:bg-[#0a1f3f] disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isAccepting ? (
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Accepting...
                        </div>
                      ) : (
                        "Accept Delivery"
                      )}
                    </InteractiveButton>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Complaints Dashboard Modal */}
      <ComplaintsDashboardModal
        isOpen={isComplaintsModalOpen}
        onClose={() => setIsComplaintsModalOpen(false)}
      />
    </MemberWrapper>
  );
};

export default MemberAvailableDeliveriesPage;
