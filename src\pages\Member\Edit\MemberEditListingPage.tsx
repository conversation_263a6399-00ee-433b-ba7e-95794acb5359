import React, { useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { MemberWrapper } from "../../../components/MemberWrapper";
import { InteractiveButton } from "../../../components/InteractiveButton";
import { MkdInputV2 } from "../../../components/MkdInputV2";
import { MkdLoader } from "../../../components/MkdLoader";
import { Skeleton } from "../../../components/Skeleton";
import {
  useListingQuery,
  useUpdateListingMutation,
  useMemberCategoriesQuery,
} from "../../../query/useListing";

const schema = yup.object({
  name: yup.string().required("Listing name is required"),
  description: yup.string().required("Description is required"),
  price: yup
    .number()
    .typeError("Price must be a valid number")
    .positive("Price must be greater than 0")
    .required("Price is required"),
  category: yup.string().required("Category is required"),
  image: yup.string().url("Image must be a valid URL").nullable(),
  status: yup.string().required("Status is required"),
});

interface FormValues {
  name: string;
  description: string;
  price: number;
  category: string;
  image: string;
  status: string;
}

const MemberEditListingPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
    watch,
  } = useForm<FormValues>({
    resolver: yupResolver(schema),
    defaultValues: {
      name: "",
      description: "",
      price: 0,
      category: "",
      image: "",
      status: "active",
    },
  });

  // Query hooks
  const { data: listing, isLoading, error } = useListingQuery(id!);
  const { data: categoriesData, isLoading: categoriesLoading } =
    useMemberCategoriesQuery();
  const { mutate: updateListing, isPending: isUpdating } =
    useUpdateListingMutation();

  const watchedImage = watch("image");

  // Populate form when listing data is loaded
  useEffect(() => {
    if (listing && !listing.error) {
      const listingData = listing.data;
      reset({
        name: listingData?.name || "",
        description: listingData?.description || "",
        price: listingData?.price || 0,
        category: listingData?.category || "",
        image: listingData?.images?.[0] || "",
        status: listingData?.status || "active",
      });
    }
  }, [listing, reset]);

  const onSubmit = (data: FormValues) => {
    updateListing(
      {
        id: id!,
        data: {
          name: data.name,
          description: data.description,
          price: data.price,
          category: data.category,
          image: data.image || "",
          status: data.status,
        },
      },
      {
        onSuccess: () => {
          navigate("/member/listings");
        },
      }
    );
  };

  // Loading state
  if (isLoading || categoriesLoading) {
    return (
      <MemberWrapper>
        <div className="p-6 bg-[#001f3f] min-h-screen">
          <div className="mb-6">
            <InteractiveButton
              onClick={() => navigate("/member/listings")}
              className="text-white hover:text-gray-300 mb-4"
            >
              ← Back to My Listings
            </InteractiveButton>
            <h1 className="text-3xl font-bold text-white mb-2">Edit Listing</h1>
            <p className="text-gray-300">Update your listing details</p>
          </div>
          <div className="bg-white rounded-lg p-6">
            <div className="space-y-6">
              <div>
                <Skeleton className="h-4 w-24 mb-2" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div>
                <Skeleton className="h-4 w-20 mb-2" />
                <Skeleton className="h-24 w-full" />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Skeleton className="h-4 w-16 mb-2" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div>
                  <Skeleton className="h-4 w-20 mb-2" />
                  <Skeleton className="h-10 w-full" />
                </div>
              </div>
              <div>
                <Skeleton className="h-4 w-28 mb-2" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div>
                <Skeleton className="h-4 w-16 mb-2" />
                <Skeleton className="h-10 w-full" />
              </div>
              <Skeleton className="h-12 w-full" />
            </div>
          </div>
        </div>
      </MemberWrapper>
    );
  }

  // Error state
  if (error || (listing && listing.error)) {
    return (
      <MemberWrapper>
        <div className="p-6 bg-[#001f3f] min-h-screen">
          <div className="mb-6">
            <InteractiveButton
              onClick={() => navigate("/member/listings")}
              className="text-white hover:text-gray-300 mb-4"
            >
              ← Back to My Listings
            </InteractiveButton>
            <h1 className="text-3xl font-bold text-white mb-2">Edit Listing</h1>
            <p className="text-gray-300">Update your listing details</p>
          </div>
          <div className="bg-white rounded-lg p-6">
            <div className="text-center py-10 text-red-500">
              {listing?.message || "Failed to load listing"}
            </div>
          </div>
        </div>
      </MemberWrapper>
    );
  }

  // Prepare categories for dropdown
  const categories = [
    { value: "", label: "Select a category" },
    ...(categoriesData?.data || []).map((cat: any) => ({
      value: cat.name,
      label: cat.label,
    })),
  ];

  return (
    <MemberWrapper>
      <div className="p-6 bg-[#001f3f] min-h-screen">
        <div className="mb-6">
          <InteractiveButton
            onClick={() => navigate("/member/listings")}
            className="text-white hover:text-gray-300 mb-4"
          >
            ← Back to My Listings
          </InteractiveButton>
          <h1 className="text-3xl font-bold text-white mb-2">Edit Listing</h1>
          <p className="text-gray-300">Update your listing details</p>
        </div>

        <div className="bg-white rounded-lg p-6">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Listing Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Listing Name *
              </label>
              <MkdInputV2
                placeholder="Enter listing name"
                {...register("name")}
                required
              >
                <MkdInputV2.Field />
              </MkdInputV2>
              {errors.name && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.name.message}
                </p>
              )}
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#e53e3e] focus:border-transparent"
                rows={4}
                placeholder="Describe your item in detail"
                {...register("description")}
                required
              />
              {errors.description && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.description.message}
                </p>
              )}
            </div>

            {/* Price and Category Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price *
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                    $
                  </span>
                  <MkdInputV2
                    type="number"
                    placeholder="0.00"
                    className="pl-8"
                    {...register("price", { valueAsNumber: true })}
                    required
                  >
                    <MkdInputV2.Field min="0" step="0.01" />
                  </MkdInputV2>
                </div>
                {errors.price && (
                  <p className="text-red-500 text-sm mt-1">
                    {errors.price.message}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category *
                </label>
                <select
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#e53e3e] focus:border-transparent"
                  {...register("category")}
                  required
                >
                  {categories.map((cat) => (
                    <option key={cat.value} value={cat.value}>
                      {cat.label}
                    </option>
                  ))}
                </select>
                {errors.category && (
                  <p className="text-red-500 text-sm mt-1">
                    {errors.category.message}
                  </p>
                )}
              </div>
            </div>

            {/* Image URL */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Image URL (optional)
              </label>
              <MkdInputV2
                type="url"
                placeholder="https://example.com/image.jpg"
                {...register("image")}
              >
                <MkdInputV2.Field />
              </MkdInputV2>
              {errors.image && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.image.message}
                </p>
              )}
              <p className="text-sm text-gray-500 mt-1">
                Provide a URL to an image of your item
              </p>
            </div>

            {/* Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#e53e3e] focus:border-transparent"
                {...register("status")}
              >
                <option value="active">Active</option>
                <option value="draft">Draft</option>
                <option value="sold">Sold</option>
                <option value="expired">Expired</option>
              </select>
              {errors.status && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.status.message}
                </p>
              )}
              <p className="text-sm text-gray-500 mt-1">
                Active listings will be visible in the marketplace
              </p>
            </div>

            {/* Image Preview */}
            {watchedImage && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Image Preview
                </label>
                <div className="border border-gray-300 rounded-md p-4">
                  <img
                    src={watchedImage}
                    alt="Preview"
                    className="max-w-xs max-h-48 object-cover rounded-md"
                    onError={(e) => {
                      e.currentTarget.style.display = "none";
                    }}
                  />
                </div>
              </div>
            )}

            {/* Submit Buttons */}
            <div className="flex gap-4 pt-6">
              <InteractiveButton
                type="submit"
                disabled={isUpdating}
                className="flex-1 bg-[#e53e3e] text-white py-3 px-6 rounded-md hover:bg-[#c53030] disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {isUpdating ? "Saving..." : "Save Changes"}
              </InteractiveButton>

              <InteractiveButton
                type="button"
                onClick={() => navigate("/member/listings")}
                disabled={isUpdating}
                className="flex-1 border border-gray-300 text-gray-700 py-3 px-6 rounded-md hover:bg-gray-50 disabled:opacity-50"
              >
                Cancel
              </InteractiveButton>
            </div>
          </form>
        </div>
      </div>
    </MemberWrapper>
  );
};

export default MemberEditListingPage;
