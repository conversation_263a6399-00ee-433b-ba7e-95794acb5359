import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useSDK } from "../hooks/useSDK";
import { useToast } from "../components/Toast";
import { ToastStatusEnum } from "../utils/Enums";

// Interfaces
export interface ITestimonial {
  id: number;
  name: string;
  rating: number;
  testimonial: string;
  avatar: string;
  title?: string;
  isFeatured: boolean;
  createdAt: string;
}

export interface IFAQ {
  id: number;
  question: string;
  answer: string;
  category: string;
  isFeatured: boolean;
  viewCount: number;
  createdAt: string;
}

export interface IContactInfo {
  id: number;
  title: string;
  phone?: string;
  email?: string;
  address?: string;
  description?: string;
  iconType: string;
  department: string;
  createdAt: string;
}

export interface ILandingContent {
  [key: string]: {
    value: string;
    type: string;
    id: number;
    createdAt: string;
  };
}

export interface IChatMessage {
  id: number;
  text: string;
  isFromUser: boolean;
  senderName: string;
  messageType: string;
  createdAt: string;
}

export interface IChatSession {
  sessionId: string;
  status: string;
}

// Testimonials Query
export const useTestimonialsQuery = (featured?: boolean) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["testimonials", { featured }],
    queryFn: async () => {
      console.log("🚀 Fetching testimonials, featured:", featured);

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/public/testimonials",
          method: "GET",
          params: featured ? { featured: 'true' } : {},
        });
        console.log("📡 Testimonials API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Testimonials API Call Error:", error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};

// FAQs Query
export const useFAQsQuery = (category?: string, featured?: boolean) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["faqs", { category, featured }],
    queryFn: async () => {
      console.log("🚀 Fetching FAQs, category:", category, "featured:", featured);

      try {
        const params: any = {};
        if (category) params.category = category;
        if (featured) params.featured = 'true';

        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/public/faqs",
          method: "GET",
          params,
        });
        console.log("📡 FAQs API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ FAQs API Call Error:", error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Contact Info Query
export const useContactInfoQuery = (department?: string) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["contact-info", { department }],
    queryFn: async () => {
      console.log("🚀 Fetching contact info, department:", department);

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/public/contact-info",
          method: "GET",
          params: department ? { department } : {},
        });
        console.log("📡 Contact Info API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Contact Info API Call Error:", error);
        throw error;
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Landing Content Query
export const useLandingContentQuery = (section?: string) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["landing-content", { section }],
    queryFn: async () => {
      console.log("🚀 Fetching landing content, section:", section);

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/public/landing-content",
          method: "GET",
          params: section ? { section } : {},
        });
        console.log("📡 Landing Content API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Landing Content API Call Error:", error);
        throw error;
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Start Chat Session Mutation
export const useStartChatMutation = () => {
  const { sdk } = useSDK();
  const { success, error } = useToast();

  return useMutation({
    mutationFn: async ({ userEmail, userName }: { userEmail?: string; userName?: string }) => {
      console.log("🚀 Starting chat session:", { userEmail, userName });

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/public/chat/start",
          method: "POST",
          data: { userEmail, userName },
        });
        console.log("📡 Start Chat API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Start Chat API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log("✅ Chat session started successfully:", data);
    },
    onError: (error: any) => {
      console.error("❌ Start chat session failed:", error);
      const errorMessage = error?.response?.data?.message || "Failed to start chat session";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};

// Send Chat Message Mutation
export const useSendChatMessageMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { error } = useToast();

  return useMutation({
    mutationFn: async ({ 
      sessionId, 
      message, 
      senderName 
    }: {
      sessionId: string;
      message: string;
      senderName?: string;
    }) => {
      console.log("🚀 Sending chat message:", { sessionId, message, senderName });

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/public/chat/message",
          method: "POST",
          data: { sessionId, message, senderName },
        });
        console.log("📡 Send Message API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Send Message API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (data, variables) => {
      console.log("✅ Message sent successfully:", data);
      
      // Invalidate chat messages to refresh the conversation
      queryClient.invalidateQueries({ 
        queryKey: ["chat-messages", variables.sessionId] 
      });
    },
    onError: (error: any) => {
      console.error("❌ Send message failed:", error);
      const errorMessage = error?.response?.data?.message || "Failed to send message";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};

// Chat Messages Query
export const useChatMessagesQuery = (sessionId: string) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["chat-messages", sessionId],
    queryFn: async () => {
      console.log("🚀 Fetching chat messages for session:", sessionId);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/public/chat/${sessionId}/messages`,
          method: "GET",
        });
        console.log("📡 Chat Messages API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Chat Messages API Call Error:", error);
        throw error;
      }
    },
    enabled: !!sessionId,
    staleTime: 30 * 1000, // 30 seconds
    cacheTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 1000, // Refetch every 5 seconds for real-time chat
  });
};

// End Chat Session Mutation
export const useEndChatMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { success, error } = useToast();

  return useMutation({
    mutationFn: async (sessionId: string) => {
      console.log("🚀 Ending chat session:", sessionId);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/public/chat/${sessionId}/end`,
          method: "POST",
        });
        console.log("📡 End Chat API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ End Chat API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (data, sessionId) => {
      console.log("✅ Chat session ended successfully:", data);
      success("Chat session ended", ToastStatusEnum.SUCCESS);
      
      // Clear chat messages cache
      queryClient.removeQueries({ 
        queryKey: ["chat-messages", sessionId] 
      });
    },
    onError: (error: any) => {
      console.error("❌ End chat session failed:", error);
      const errorMessage = error?.response?.data?.message || "Failed to end chat session";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};
