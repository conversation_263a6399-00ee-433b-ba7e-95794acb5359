import React from "react";
import { Modal } from "@/components/Modal";
import InteractiveButton from "@/components/InteractiveButton/InteractiveButton";
import { TrashIcon } from "@/assets/svgs";

interface DeleteListingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  listingName: string;
}

const DeleteListingModal: React.FC<DeleteListingModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  listingName,
}) => {
  if (!isOpen) {
    return null;
  }

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={onClose}
      title=""
      modalHeader={false}
      classes={{
        modal: "flex items-center justify-center",
        modalDialog:
          "bg-white rounded-lg shadow-xl p-8 w-full max-w-md mx-auto text-black",
      }}
    >
      <div className="text-center">
        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
          <TrashIcon className="w-8 h-8 text-[#F52D2A]" />
        </div>

        <h2 className="text-2xl font-bold text-gray-800 mb-4">
          Delete Listing
        </h2>

        <p className="text-gray-600 mb-6">
          Are you sure you want to delete "{listingName}"?
        </p>

        <div className="bg-gray-100 p-4 rounded-md text-sm text-gray-500 mb-8">
          This action cannot be undone. The listing will be permanently removed
          from your account.
        </div>

        <div className="flex justify-center gap-4">
          <InteractiveButton
            onClick={onClose}
            className="border border-gray-300 text-gray-800 px-8 py-3 rounded-md hover:bg-gray-100 font-semibold"
          >
            Cancel
          </InteractiveButton>
          <InteractiveButton
            onClick={onConfirm}
            className="bg-[#F52D2A] text-white px-8 py-3 rounded-md hover:bg-red-700 font-semibold"
          >
            Delete Listing
          </InteractiveButton>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteListingModal;
