import React from "react";
import { Skeleton } from "../Skeleton";

interface Offer {
  id: number;
  buyerName: string;
  buyerAvatar: string;
  date: string;
  status: "Pending" | "Rejected";
  amount: string;
  quantity: number;
  message: string;
}

interface OffersReceivedModalProps {
  isOpen: boolean;
  onClose: () => void;
  itemName: string;
  offers: Offer[];
  onAcceptOffer: (offerId: number) => void;
  onRejectOffer: (offerId: number) => void;
  isLoading?: boolean;
  isAccepting?: boolean;
  isRejecting?: boolean;
}

const OffersReceivedModal: React.FC<OffersReceivedModalProps> = ({
  isOpen,
  onClose,
  itemName,
  offers,
  onAcceptOffer,
  onRejectOffer,
  isLoading = false,
  isAccepting = false,
  isRejecting = false,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-[520px] max-w-[90vw] max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
          <h2 className="text-base font-medium text-[#0F2C59]">
            Offers Received on "{itemName}"
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-lg font-normal w-6 h-6 flex items-center justify-center"
          >
            ×
          </button>
        </div>

        {/* Offers List */}
        <div className="px-6 py-4 space-y-3">
          {isLoading ? (
            // Loading skeleton
            [...Array(3)].map((_, index) => (
              <div
                key={index}
                className="border border-gray-200 rounded-md p-4"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <Skeleton className="w-10 h-10 rounded-full" />
                    <div>
                      <Skeleton className="h-4 w-24 mb-1" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                  </div>
                  <Skeleton className="h-6 w-16" />
                </div>
                <Skeleton className="h-16 w-full mb-3" />
                <div className="flex justify-between items-center">
                  <Skeleton className="h-4 w-20" />
                  <div className="flex space-x-2">
                    <Skeleton className="h-8 w-16" />
                    <Skeleton className="h-8 w-16" />
                  </div>
                </div>
              </div>
            ))
          ) : offers.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p className="text-lg mb-2">No offers yet</p>
              <p className="text-sm">
                When buyers make offers on this listing, they'll appear here.
              </p>
            </div>
          ) : (
            offers.map((offer) => (
              <div
                key={offer.id}
                className="border border-gray-200 rounded-md p-4"
              >
                {/* Offer Header */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-200">
                      <img
                        src={offer.buyerAvatar}
                        alt={offer.buyerName}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="font-medium text-[#0F2C59] text-sm">
                          {offer.buyerName}
                        </span>
                        <span className="text-xs text-gray-500">
                          {offer.date}
                        </span>
                        <span
                          className={`text-xs px-2 py-0.5 rounded text-white ${
                            offer.status === "Pending"
                              ? "bg-orange-500"
                              : "bg-red-500"
                          }`}
                        >
                          {offer.status}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-[#0F2C59] text-base">
                      eBa$ {offer.amount}
                    </div>
                    <div className="text-xs text-gray-500">
                      Qty: {offer.quantity}
                    </div>
                  </div>
                </div>

                {/* Offer Message */}
                <div className="mb-4">
                  <p className="text-sm text-gray-600 leading-relaxed">
                    {offer.message}
                  </p>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end space-x-2">
                  {offer.status === "Pending" ? (
                    <>
                      <button
                        onClick={() => onRejectOffer(offer.id)}
                        disabled={isRejecting || isAccepting}
                        className="px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded hover:bg-gray-200 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isRejecting ? (
                          <div className="flex items-center">
                            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-700 mr-1"></div>
                            Rejecting...
                          </div>
                        ) : (
                          "Reject"
                        )}
                      </button>
                      <button
                        onClick={() => onAcceptOffer(offer.id)}
                        disabled={isAccepting || isRejecting}
                        className="px-3 py-1.5 text-xs font-medium text-white bg-[#E63946] rounded hover:bg-red-700 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isAccepting ? (
                          <div className="flex items-center">
                            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1"></div>
                            Accepting...
                          </div>
                        ) : (
                          "Accept"
                        )}
                      </button>
                    </>
                  ) : (
                    <>
                      <button
                        disabled
                        className="px-3 py-1.5 text-xs font-medium text-gray-500 bg-gray-100 border border-gray-300 rounded cursor-not-allowed"
                      >
                        Reject
                      </button>
                      <button
                        disabled
                        className="px-3 py-1.5 text-xs font-medium text-gray-500 bg-gray-100 border border-gray-300 rounded cursor-not-allowed"
                      >
                        Accept
                      </button>
                    </>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default OffersReceivedModal;
