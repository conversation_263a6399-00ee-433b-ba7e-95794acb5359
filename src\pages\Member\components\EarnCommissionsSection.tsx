import React from "react";

const EarnCommissionsSection = () => {
  return (
    <section 
      className="py-20 px-4 sm:px-6 lg:px-8"
      style={{
        background: "linear-gradient(135deg, #0D3166 0%, #1E40AF 100%)"
      }}
    >
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="text-white text-center lg:text-left">
            {/* Main Heading */}
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Earn Commissions Easily
            </h2>

            {/* Description */}
            <p className="text-xl text-blue-100 mb-8 leading-relaxed">
              Get paid for sharing the platform. Invite friends to eBaDollar and earn a 
              commission on their first trade or purchase.
            </p>
          </div>

          {/* Right Content - Icon and How It Works */}
          <div className="text-white">
            {/* Red Circle Icon */}
            <div className="flex justify-center lg:justify-start mb-8">
              <div 
                className="w-24 h-24 rounded-full flex items-center justify-center"
                style={{ backgroundColor: "#F52D2A" }}
              >
                <svg className="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
            </div>

            {/* How It Works List */}
            <div className="space-y-4">
              <h3 className="text-xl font-bold mb-6">How It Works</h3>
              
              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5 text-green-400 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                </svg>
                <span className="text-blue-100">Share your unique referral link with friends and family</span>
              </div>

              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5 text-green-400 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                </svg>
                <span className="text-blue-100">They sign up and make their first transaction</span>
              </div>

              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5 text-green-400 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                </svg>
                <span className="text-blue-100">You earn 5% commission on their transaction value</span>
              </div>
            </div>

            {/* CTA Button */}
            <div className="mt-8">
              <button 
                className="px-8 py-3 text-white font-semibold rounded-md transition-colors hover:opacity-90"
                style={{ backgroundColor: "#F52D2A" }}
              >
                Start Earning Now
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default EarnCommissionsSection;
