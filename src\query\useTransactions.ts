import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useSDK } from "../hooks/useSDK";
import { useToast } from "../components/Toast";
import { ToastStatusEnum } from "../utils/Enums";

// Interfaces
export interface ITransactionFilters {
  page?: number;
  limit?: number;
  search?: string;
  dateRange?: string;
  transactionType?: string;
  status?: string;
  tab?: string;
}

export interface ITransaction {
  id: number;
  date: string;
  listing: string;
  type: string;
  counterparty: string;
  amount: string;
  fee: string;
  net_received_paid: string;
  status: string;
  listingId?: number;
  description?: string;
  createdAt: string;
  updatedAt: string;
  transactionGroupId?: string;
  referenceId?: string;
  paymentMethod?: string;
}

export interface ITransactionSummary {
  sales: {
    total: string;
    count: number;
    currency: string;
  };
  purchases: {
    total: string;
    count: number;
    currency: string;
  };
  fees: {
    total: string;
    average: string;
    currency: string;
  };
}

// Transactions Query
export const useTransactionsQuery = (filters: ITransactionFilters = {}) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["transactions", filters],
    queryFn: async () => {
      console.log("🚀 Fetching transactions with filters:", filters);

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/transactions",
          method: "GET",
          params: filters,
        });
        console.log("📡 Transactions API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Transactions API Call Error:", error);
        throw error;
      }
    },
    staleTime: 30 * 1000, // 30 seconds
    cacheTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Transaction Summary Query
export const useTransactionSummaryQuery = () => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["transaction-summary"],
    queryFn: async () => {
      console.log("🚀 Fetching transaction summary");

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/transactions/summary",
          method: "GET",
        });
        console.log("📡 Transaction Summary API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Transaction Summary API Call Error:", error);
        throw error;
      }
    },
    staleTime: 60 * 1000, // 1 minute
    cacheTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Single Transaction Query
export const useTransactionQuery = (transactionId: number) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["transaction", transactionId],
    queryFn: async () => {
      console.log("🚀 Fetching transaction details:", transactionId);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/transactions/${transactionId}`,
          method: "GET",
        });
        console.log("📡 Transaction Details API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Transaction Details API Call Error:", error);
        throw error;
      }
    },
    enabled: !!transactionId && transactionId > 0,
    staleTime: 30 * 1000, // 30 seconds
    cacheTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Export Transaction Data Mutation (placeholder for future implementation)
export const useExportTransactionsMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { success, error } = useToast();

  return useMutation({
    mutationFn: async ({ 
      format, 
      filters 
    }: {
      format: "csv" | "excel" | "pdf";
      filters?: ITransactionFilters;
    }) => {
      console.log("🚀 Exporting transactions:", { format, filters });

      try {
        // This would be implemented when export functionality is needed
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/transactions/export",
          method: "POST",
          data: { format, filters },
        });
        console.log("📡 Export Transactions API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Export Transactions API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (data, variables) => {
      console.log("✅ Successfully exported transactions:", data);
      success(`Transactions exported as ${variables.format.toUpperCase()}`, ToastStatusEnum.SUCCESS);
    },
    onError: (error: any) => {
      console.error("❌ Export transactions failed:", error);
      const errorMessage = error?.response?.data?.message || "Failed to export transactions";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};

// Dispute Transaction Mutation (placeholder for future implementation)
export const useDisputeTransactionMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { success, error } = useToast();

  return useMutation({
    mutationFn: async ({ 
      transactionId, 
      reason, 
      description 
    }: {
      transactionId: number;
      reason: string;
      description: string;
    }) => {
      console.log("🚀 Disputing transaction:", { transactionId, reason, description });

      try {
        // This would be implemented when dispute functionality is needed
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/transactions/${transactionId}/dispute`,
          method: "POST",
          data: { reason, description },
        });
        console.log("📡 Dispute Transaction API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Dispute Transaction API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (data, variables) => {
      console.log("✅ Successfully disputed transaction:", data);
      success("Transaction dispute submitted successfully", ToastStatusEnum.SUCCESS);
      
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ["transactions"] });
      queryClient.invalidateQueries({ queryKey: ["transaction", variables.transactionId] });
      queryClient.invalidateQueries({ queryKey: ["transaction-summary"] });
    },
    onError: (error: any) => {
      console.error("❌ Dispute transaction failed:", error);
      const errorMessage = error?.response?.data?.message || "Failed to submit dispute";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};

// Request Refund Mutation (placeholder for future implementation)
export const useRequestRefundMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { success, error } = useToast();

  return useMutation({
    mutationFn: async ({ 
      transactionId, 
      reason, 
      amount 
    }: {
      transactionId: number;
      reason: string;
      amount?: number;
    }) => {
      console.log("🚀 Requesting refund:", { transactionId, reason, amount });

      try {
        // This would be implemented when refund functionality is needed
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/transactions/${transactionId}/refund`,
          method: "POST",
          data: { reason, amount },
        });
        console.log("📡 Request Refund API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Request Refund API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (data, variables) => {
      console.log("✅ Successfully requested refund:", data);
      success("Refund request submitted successfully", ToastStatusEnum.SUCCESS);
      
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ["transactions"] });
      queryClient.invalidateQueries({ queryKey: ["transaction", variables.transactionId] });
      queryClient.invalidateQueries({ queryKey: ["transaction-summary"] });
    },
    onError: (error: any) => {
      console.error("❌ Request refund failed:", error);
      const errorMessage = error?.response?.data?.message || "Failed to request refund";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};

// Helper function to format transaction status for display
export const formatTransactionStatus = (status: string): { text: string; color: string } => {
  switch (status.toLowerCase()) {
    case "completed":
      return { text: "Completed", color: "text-green-600 bg-green-100" };
    case "in dispute":
    case "disputed":
      return { text: "In Dispute", color: "text-red-600 bg-red-100" };
    case "refunded":
      return { text: "Refunded", color: "text-blue-600 bg-blue-100" };
    case "pending":
      return { text: "Pending", color: "text-yellow-600 bg-yellow-100" };
    default:
      return { text: status, color: "text-gray-600 bg-gray-100" };
  }
};

// Helper function to format transaction type for display
export const formatTransactionType = (type: string): { text: string; color: string } => {
  switch (type.toLowerCase()) {
    case "sale":
      return { text: "Sale", color: "text-green-600" };
    case "purchase":
      return { text: "Purchase", color: "text-blue-600" };
    case "transaction":
      return { text: "Transaction", color: "text-gray-600" };
    default:
      return { text: type, color: "text-gray-600" };
  }
};
