import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useSDK } from "../hooks/useSDK";
import { useToast } from "../components/Toast";
import { ToastStatusEnum } from "../utils/Enums";

// Interfaces
export interface IRewardsOverview {
  totalLoyaltyRewards: string;
  thisMonthRewards: string;
  redeemedThisMonth: string;
  availableBalance: string;
  loyaltyPointsBalance: number;
}

export interface IPerformanceMetrics {
  lastMonthSales: string;
  lastMonthPurchases: string;
  creditScore: number;
  starRating: string;
  reviewCount: number;
  salesGrowth: string;
  purchasesGrowth: string;
}

export interface IChartData {
  loyalty: Array<{ name: string; value: number }>;
  referral: Array<{ name: string; value: number }>;
  rewardsRedeemed: Array<{ name: string; value: number }>;
  commissionsRedeemed: Array<{ name: string; value: number }>;
}

export interface IReferralData {
  code: string;
  link: string;
  totalReferred: number;
  activeThisMonth: number;
  commissionsEarned: string;
  commissionsThisMonth: string;
  signupBonus: string;
  transactionCommission: string;
}

export interface IMonthlyBreakdown {
  month: string;
  reward: string;
  commission: string;
  points: string;
  loyaltyStatus: string;
  commissionStatus: string;
}

export interface IReferralHistory {
  id: number;
  refereeName: string;
  refereeEmail: string;
  commissionAmount: string;
  status: string;
  referralDate: string;
  signupDate: string;
}

// Rewards Overview Query
export const useRewardsOverviewQuery = () => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["rewards-overview"],
    queryFn: async () => {
      console.log("🚀 Fetching rewards overview");

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/rewards/overview",
          method: "GET",
        });
        console.log("📡 Rewards Overview API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Rewards Overview API Call Error:", error);
        throw error;
      }
    },
    staleTime: 30 * 1000, // 30 seconds
    cacheTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Performance Metrics Query
export const usePerformanceMetricsQuery = () => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["performance-metrics"],
    queryFn: async () => {
      console.log("🚀 Fetching performance metrics");

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/rewards/performance",
          method: "GET",
        });
        console.log("📡 Performance Metrics API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Performance Metrics API Call Error:", error);
        throw error;
      }
    },
    staleTime: 60 * 1000, // 1 minute
    cacheTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Chart Data Query
export const useRewardsChartDataQuery = () => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["rewards-chart-data"],
    queryFn: async () => {
      console.log("🚀 Fetching rewards chart data");

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/rewards/charts",
          method: "GET",
        });
        console.log("📡 Rewards Chart Data API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Rewards Chart Data API Call Error:", error);
        throw error;
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Referral Data Query
export const useReferralDataQuery = () => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["referral-data"],
    queryFn: async () => {
      console.log("🚀 Fetching referral data");

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/rewards/referral",
          method: "GET",
        });
        console.log("📡 Referral Data API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Referral Data API Call Error:", error);
        throw error;
      }
    },
    staleTime: 30 * 1000, // 30 seconds
    cacheTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Monthly Breakdown Query
export const useMonthlyBreakdownQuery = (year?: number) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["monthly-breakdown", year],
    queryFn: async () => {
      console.log("🚀 Fetching monthly breakdown for year:", year);

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/rewards/monthly-breakdown",
          method: "GET",
          params: year ? { year } : {},
        });
        console.log("📡 Monthly Breakdown API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Monthly Breakdown API Call Error:", error);
        throw error;
      }
    },
    staleTime: 60 * 1000, // 1 minute
    cacheTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Referral History Query
export const useReferralHistoryQuery = (page: number = 1, limit: number = 10) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["referral-history", page, limit],
    queryFn: async () => {
      console.log("🚀 Fetching referral history:", { page, limit });

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/referral/history",
          method: "GET",
          params: { page, limit },
        });
        console.log("📡 Referral History API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Referral History API Call Error:", error);
        throw error;
      }
    },
    staleTime: 30 * 1000, // 30 seconds
    cacheTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Share Referral Link Mutation
export const useShareReferralMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { success, error } = useToast();

  return useMutation({
    mutationFn: async ({ 
      method, 
      recipients 
    }: {
      method: string;
      recipients: string[];
    }) => {
      console.log("🚀 Sharing referral link:", { method, recipients });

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/referral/share",
          method: "POST",
          data: { method, recipients },
        });
        console.log("📡 Share Referral API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Share Referral API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (data, variables) => {
      console.log("✅ Successfully shared referral link:", data);
      success(`Referral link shared successfully via ${variables.method}`, ToastStatusEnum.SUCCESS);
      
      // Invalidate referral data to refresh stats
      queryClient.invalidateQueries({ queryKey: ["referral-data"] });
    },
    onError: (error: any) => {
      console.error("❌ Share referral link failed:", error);
      const errorMessage = error?.response?.data?.message || "Failed to share referral link";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};

// Invite by Email Mutation
export const useInviteByEmailMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { success, error } = useToast();

  return useMutation({
    mutationFn: async ({ 
      emails, 
      message 
    }: {
      emails: string[];
      message?: string;
    }) => {
      console.log("🚀 Sending email invitations:", { emails, message });

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/referral/invite-email",
          method: "POST",
          data: { emails, message },
        });
        console.log("📡 Invite by Email API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Invite by Email API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (data, variables) => {
      console.log("✅ Successfully sent email invitations:", data);
      success(`Invitations sent to ${variables.emails.length} recipients`, ToastStatusEnum.SUCCESS);
      
      // Invalidate referral data to refresh stats
      queryClient.invalidateQueries({ queryKey: ["referral-data"] });
      queryClient.invalidateQueries({ queryKey: ["referral-history"] });
    },
    onError: (error: any) => {
      console.error("❌ Send email invitations failed:", error);
      const errorMessage = error?.response?.data?.message || "Failed to send invitations";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};

// Update Referral Code Mutation
export const useUpdateReferralCodeMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { success, error } = useToast();

  return useMutation({
    mutationFn: async ({ newCode }: { newCode: string }) => {
      console.log("🚀 Updating referral code:", { newCode });

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/referral/code",
          method: "PUT",
          data: { newCode },
        });
        console.log("📡 Update Referral Code API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Update Referral Code API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log("✅ Successfully updated referral code:", data);
      success("Referral code updated successfully", ToastStatusEnum.SUCCESS);
      
      // Invalidate referral data to refresh with new code
      queryClient.invalidateQueries({ queryKey: ["referral-data"] });
    },
    onError: (error: any) => {
      console.error("❌ Update referral code failed:", error);
      const errorMessage = error?.response?.data?.message || "Failed to update referral code";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};
